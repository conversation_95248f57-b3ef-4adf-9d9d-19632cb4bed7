import jsonlines
import random, os
from datasketch import MinHash
from datasketch import MinHashLSH
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from collections import defaultdict, Counter
import numpy as np
from pprint import pprint
import sys

def open_jsonl(file_path):
    with jsonlines.open(file_path) as f:
        return [l for l in f]

def save_jsonl(save_path, dataset):
    with jsonlines.open(save_path, 'w') as f:
        f.write_all(dataset)
        
def shingle(data_record, k=3):
    id = data_record['id']
    text = data_record['query_and_response'][0]['content']
    tokens = text.lower().split()

    return {
        'id': id,
        'tokens': set([' '.join(tokens[i:i+k]) for i in range(len(tokens)-k+1)])
    }

def get_query(example):
    for conv_dict in example['query_and_response']:
        if conv_dict['from'] == 'user':
            text_content = str(conv_dict['content'])
            return text_content
        else: continue
    return example['query_and_response'][0]['content']

def lookup_data(data, id):
    for record in data:
        if record['id'] == id:
            return record
    return None

def group_by_dataset(data):
    grouped_dt = {}
    for record in data:
        dt_id = record['id'].split("_")[0]
        if not dt_id in grouped_dt:
            grouped_dt[dt_id] = 0
        if record['dedup_priority']==1: grouped_dt[dt_id] +=1

    return grouped_dt

def get_data_stat(data_dir):
    aggregated_stats = defaultdict(list)
    for file in os.listdir(data_dir):
        if file.endswith('jsonl'):
            dt = open_jsonl(os.path.join(data_dir, file))
            dt_name = file.split('_merged')[0]
            dt_stat = group_by_dataset(dt)
            aggregated_stats[dt_name] = dt_stat

    df = pd.DataFrame.from_dict(aggregated_stats, orient='index').fillna(0).astype(int)
    df.index.name = 'Dataset'
    df.T.to_csv(os.path.join(data_dir, "stat_dedup.csv"))

if_dataset = open_jsonl("2_if_datasets.jsonl")
general_dataset = open_jsonl("4-1_general_datasets.jsonl")

if_dataset[0]

print(len(if_dataset))
print(len(general_dataset))

if_dataset[0]

if_dataset_shingles = [shingle(record) for record in if_dataset]
general_dataset_shingles = [shingle(record) for record in general_dataset]

num_perm = 128  # Number of MinHash permutations

if_minhashes = []
for record in if_dataset_shingles:
    m = MinHash(num_perm=num_perm)
    for token in record['tokens']:
        m.update(token.encode('utf8'))
    if_minhashes.append({'id': record['id'], 'minhash': m})


if_minhashes[0]

lsh = MinHashLSH(threshold=0.6, num_perm=num_perm)
for item in if_minhashes:
    lsh.insert(str(item['id']), item['minhash'])

# Query for similar records to a given MinHash
def minhash_search(idx, lsh, minhashses, dataset):
    print(f"Chosen data index : {idx}")
    print(f"Looking for records similar to the query\n\t{lookup_data(dataset, minhashses[idx]['id'])['query_and_response'][0]['content']}")
    result = lsh.query(minhashses[idx]['minhash'])
    print(f"Records similar to {minhashses[idx]['id']}: {result}")
    for i, r in enumerate(result):
        print(f"    {i+1}. {lookup_data(dataset, r)['query_and_response'][0]['content']}")

# To find all duplicate groups in the dataset
def build_duplicate_groups(lsh, minhashes):
    record_to_group = {}
    groups = []
    visited = set()

    for idx, item in enumerate(minhashes):
        if item['id'] in visited:
            continue
        result = set(lsh.query(item['minhash']))
        if result - visited:  # New group found
            groups.append(result)
            visited.update(result)
            for rid in result:
                record_to_group[rid] = len(groups) - 1
    return groups, record_to_group

minhash_search(random.choice(range(len(if_dataset))), lsh, if_minhashes, if_dataset)

minhash_search(random.choice(range(len(if_dataset))), lsh, if_minhashes, if_dataset)

minhash_search(random.choice(range(len(if_dataset))), lsh, if_minhashes, if_dataset)

minhash_search(random.choice(range(len(if_dataset))), lsh, if_minhashes, if_dataset)

minhash_search(random.choice(range(len(if_dataset))), lsh, if_minhashes, if_dataset)

groups, record_to_group = build_duplicate_groups(lsh, if_minhashes)

len(groups)

# grid-search for best threshold
def grid_search_thresholds(thresholds, num_perms, k_shingles, dataset):
    # collect results
    results = []

    for k in k_shingles:
        dataset_shingles = [shingle(record, k) for record in dataset]
        for num_perm in num_perms:
            print(f"Processing k={k}, num_perm={num_perm}")
            minhashes = []
            for record in dataset_shingles:
                m = MinHash(num_perm=num_perm)
                for token in record['tokens']:
                    m.update(token.encode('utf8'))
                minhashes.append({'id': record['id'], 'minhash': m})

            # iterate over thresholds
            for threshold in thresholds:
                print(f"    Processing threshold: {threshold}")

                # generate LSH index
                lsh = MinHashLSH(threshold=threshold, num_perm=num_perm)
                for item in minhashes:
                    lsh.insert(str(item['id']), item['minhash'])
                
                # get the number of unique groups
                groups, _ = build_duplicate_groups(lsh, minhashes)
                # print(f"    {len(groups)} unique groups found!")
                results.append({
                    "k": k,
                    "num_perm": num_perm,
                    "threshold": threshold,
                    "num_groups": len(groups)
                })
                del lsh
                
    return results

# thresholds = [0.1, 0.3, 0.5, 0.7, 0.9]
# num_perms = [64, 128, 256]
# k_shingles = [2, 3, 4]
# results = grid_search_thresholds(thresholds, num_perms, k_shingles, if_dataset)

# with open("grid_search_results.jsonl", "w") as f:
#     writer = jsonlines.Writer(f)
#     writer.write_all(results)
#     writer.close()

def plot_groups_vs_threshold(results):
    """
    results: list of dicts with keys:
        - "k"
        - "num_perm"
        - "threshold"
        - "num_groups"
    """
    df = pd.DataFrame(results)

    # Ensure proper dtypes
    df['k'] = df['k'].astype(int)
    df['num_perm'] = df['num_perm'].astype(int)
    df['threshold'] = df['threshold'].astype(float)
    df['num_groups'] = df['num_groups'].astype(int)

    # Sort for a nicer plot
    df = df.sort_values(['num_perm', 'k', 'threshold']).reset_index(drop=True)

    # Build the plot
    plt.figure(figsize=(12, 8))
    ax = plt.gca()

    # Use color for num_perm and marker style for k to help visual grouping
    palette = sns.color_palette("viridis", n_colors=df['num_perm'].nunique())
    num_perm_to_color = {np: c for np, c in zip(sorted(df['num_perm'].unique()), palette)}

    markers = ['o', 's', 'D', '^', 'v', 'P', 'X', '*']
    unique_k = sorted(df['k'].unique())
    k_to_marker = {k: markers[i % len(markers)] for i, k in enumerate(unique_k)}

    for _, row in df.iterrows():
        ax.scatter(
            row['threshold'],
            row['num_groups'],
            color=num_perm_to_color[row['num_perm']],
            marker=k_to_marker[row['k']],
            s=70,
            alpha=0.9,
            edgecolor='k',
            linewidth=0.5
        )
        # Annotate each point with "num_perm, k"
        ax.annotate(f"{row['num_perm']}, {row['k']}",
                    (row['threshold'], row['num_groups']),
                    textcoords="offset points",
                    xytext=(5, 5), ha='left', fontsize=9)

    # Legends for num_perm (color) and k (marker)
    # Build custom legend handles
    from matplotlib.lines import Line2D
    
    color_handles = [
        Line2D([0], [0], marker='o', color='w',
               label=f"num_perm={np}",
               markerfacecolor=num_perm_to_color[np], markersize=8, markeredgecolor='k')
        for np in sorted(df['num_perm'].unique())
    ]
    marker_handles = [
        Line2D([0], [0], marker=k_to_marker[k], color='w',
               label=f"k={k}",
               markerfacecolor='gray', markersize=8, markeredgecolor='k')
        for k in sorted(df['k'].unique())
    ]

    first_legend = ax.legend(handles=color_handles, title="num_perm (color)", loc='lower right')
    ax.add_artist(first_legend)
    ax.legend(handles=marker_handles, title="k (marker)", loc='center right')

    ax.set_title("Duplicate Group Count vs Threshold")
    ax.set_xlabel("Threshold")
    ax.set_ylabel("Number of Groups")

    ax.grid(True, linestyle='--', alpha=0.3)
    plt.tight_layout()
    plt.savefig("threshold_testing.png")
    plt.show()

# Example usage:
plot_groups_vs_threshold(results)

from rensa import RMinHash, CMinHash, RMinHashLSH
from tqdm import tqdm

# Define a function to generate MinHash (works for RMinHash, CMinHash)
def generate_minhash_signature(text, minhash_class, num_perm=128, seed=42):
    m = minhash_class(num_perm=num_perm, seed=seed)
    m.update(text.split())
    return m

def deduplicate_dataset_direct(dataset, minhash_class=RMinHash, num_perm=128, desc="Deduplicating"):
    unique_hashes = set()
    deduplicated_indices = []
   
    for idx, example in tqdm(enumerate(dataset), total=len(dataset), desc=desc):
        minhash_obj = generate_minhash_signature(example['query_and_response'][0]['content'], minhash_class, num_perm)
        hash_tuple = tuple(minhash_obj.digest())
       
        if hash_tuple not in unique_hashes:
            unique_hashes.add(hash_tuple)
            deduplicated_indices.append(idx)
           
    return deduplicated_indices

deduplicated_indices_r = deduplicate_dataset_direct(
    if_dataset,
    minhash_class=RMinHash,
    desc='R-MinHash Deduplication')

len(deduplicated_indices_r)

deduplicated_indices_c = deduplicate_dataset_direct(
    if_dataset,
    minhash_class=CMinHash,
    desc='R-MinHash Deduplication')

len(deduplicated_indices_c)

def deduplicate_dataset_with_lsh_simple(dataset, threshold=0.7, final_jaccard_threshold=0.8, num_perm=128):
    seed = 42
    num_bands = 16 

    if num_perm % num_bands != 0:
        raise ValueError(f"num_bands ({num_bands}) must divide num_perm ({num_perm}).")

    minhashes = {} 
   
    for idx, example in tqdm(enumerate(dataset), total=len(dataset), desc="1. Generating RMinHashes"):
        text_content = str(example['query_and_response'][0]['content'])
        tokens = text_content.split()
        m = RMinHash(num_perm=num_perm, seed=seed)
        m.update(tokens)
        minhashes[idx] = m

    lsh_index = RMinHashLSH(threshold=threshold, num_perm=num_perm, num_bands=num_bands)
    for doc_id, rminhash_obj in tqdm(minhashes.items(), desc="2. Indexing into LSH"):
        lsh_index.insert(doc_id, rminhash_obj)

    to_remove = set()
    sorted_doc_ids = sorted(minhashes.keys())
    groups = []

    for doc_id in tqdm(sorted_doc_ids, desc="3. Querying LSH & Deduplicating"):
        if doc_id in to_remove:
            continue

        query_minhash = minhashes[doc_id]
        candidate_ids = lsh_index.query(query_minhash)
        groups.append(candidate_ids)

        for candidate_id in candidate_ids:
            if candidate_id == doc_id or candidate_id in to_remove:
                continue
            
            candidate_minhash = minhashes[candidate_id]
            actual_jaccard = query_minhash.jaccard(candidate_minhash)

            if actual_jaccard >= final_jaccard_threshold:
                # Keep the item with the smaller original index
                if doc_id < candidate_id:
                    to_remove.add(candidate_id)
                else:
                    to_remove.add(doc_id)
                    break 
   
    deduplicated_samples = [dataset[idx] for idx in sorted_doc_ids if idx not in to_remove]
    removed_samples = [dataset[idx] for idx in to_remove]
    return deduplicated_samples, removed_samples, groups

deduplicated_samples, removed_samples, groups = deduplicate_dataset_with_lsh_simple(if_dataset, 0.5, 0.55)

len(deduplicated_samples)

deduplicated_samples[0]

removed_samples[0]

groups

def show_chosen_reject(dataset, groups):
    idx = random.choice(range(len(groups)))
    if len(groups[idx]) > 1:
        retained_sample = dataset[0]['query_and_response'][0]['content']
        print(f"Showing example at {idx}th group index")
        print(f"Retained sample")
        print(f"\t{retained_sample}")
        print("*****"*50)
        print(f"Removed Samples")
        for n, i in enumerate(groups[idx][1:]):
            print(f"\t{n+1}. {dataset[i]['query_and_response'][0]['content']}")
        return
    show_chosen_reject(dataset, groups)

show_chosen_reject(if_dataset, groups)

os.environ['CUDA_VISIBLE_DEVICES'] = '0'

from sentence_transformers import SentenceTransformer
import numpy as np
import faiss

# ... (same code as before for data prep)

model = SentenceTransformer('all-MiniLM-L6-v2')
texts = [record['text'] for record in dataset]
embeddings = model.encode(texts, normalize_embeddings=True)
embeddings = np.asarray(embeddings, dtype='float32')

d = embeddings.shape[1]
cpu_index = faiss.IndexFlatIP(d)  # Inner Product

# >>> GPU FAISS changes begin here <<<
res = faiss.StandardGpuResources()  # Allocate GPU resources
gpu_index = faiss.index_cpu_to_gpu(res, 0, cpu_index)  # 0 = GPU ID
# Add embeddings to GPU index
gpu_index.add(embeddings)

# ...search as before, but use gpu_index...
threshold = 0.85
k = 5

for i, emb in enumerate(embeddings):
    D, I = gpu_index.search(emb.reshape(1, -1), k)
    print(f"Record {dataset[i]['id']} ({dataset[i]['text']}) - potential matches:")
    for j, idx in enumerate(I[0]):
        if idx != i and D[0][j] >= threshold:
            print(f"   -> Similar to record {dataset[idx]['id']} ({dataset[idx]['text']}), sim={D[0][j]:.3f}")


import jsonlines, random
from pprint import pprint

def open_jsonl(file_path):
    with jsonlines.open(file_path) as f:
        return [l for l in f]


# 가장 많은 데이터가 남은 0.7_256_8과 적은 데이터가 남은 0.3_64_32, 중간 수준인 0.5_128_16을 비교해보자
most = open_jsonl("/mnt/raid6/johnny/projects/WBL/dedup/2_if_datasets_dedup_results/2_if_datasets_samples_0.7_256_8_145198.jsonl")
mid = open_jsonl("/mnt/raid6/johnny/projects/WBL/dedup/2_if_datasets_dedup_results/2_if_datasets_samples_0.5_128_16_128027.jsonl")
least = open_jsonl("/mnt/raid6/johnny/projects/WBL/dedup/2_if_datasets_dedup_results/2_if_datasets_samples_0.3_128_32_88972.jsonl")

# 헬퍼 함수 1: 두 텍스트 간 겹치는 단어 강조
def highlight_intersection(text1, text2):
    """
    text1과 text2의 단어(토큰)를 비교하여 겹치는 단어를 강조 표시합니다.
    """
    words1 = set(text1.split())
    words2 = set(text2.split())
    common_words = words1.intersection(words2)

    highlighted_text2 = []
    for word in text2.split():
        if word in common_words:
            # ANSI 이스케이프 코드로 빨간색, 굵게 강조
            highlighted_text2.append(f"\033[1;31m{word}\033[0m")
        else:
            highlighted_text2.append(word)
    return " ".join(highlighted_text2)

# 헬퍼 함수 2: 제거된 샘플 중 최대 5개를 임의로 보여주는 함수
def show_removed_samples(case_name, sample_query, removed_list):
    """
    제거된 샘플 리스트에서 최대 5개를 임의로 골라 강조 표시와 함께 출력합니다.
    """
    print(f"--- {case_name} ({len(removed_list)} Removed) ---")
    if not removed_list:
        print("No sample left")
        return

    # 보여줄 샘플 수 결정 (최대 5개)
    num_to_show = min(len(removed_list), 5)
    
    # 임의 샘플 추출
    samples_to_show = random.sample(removed_list, num_to_show)

    for i, removed_sample in enumerate(samples_to_show):
        highlighted_text = highlight_intersection(sample_query, removed_sample)
        print(f"  Sample {i+1}:")
        # pprint 대신 print를 사용해야 ANSI 코드가 올바르게 렌더링됩니다.
        print(f"    {highlighted_text}")
    print("-" * 20)


# 메인 함수 수정
def find_example(most, mid, least):
    least_queries = [d['Chosen_sample'] for d in least]
    mid_queries = [d['Chosen_sample'] for d in mid]
    most_queries = [d['Chosen_sample'] for d in most]

    # 세 리스트에 모두 존재하는 쿼리들의 교집합을 찾습니다.
    intersect = set(least_queries) & set(mid_queries) & set(most_queries)
    
    if not intersect:
        print("모든 케이스에 공통적으로 존재하는 쿼리가 없습니다.")
        return

    # 교집합에서 임의의 샘플 쿼리를 하나 선택합니다.
    sample_query = random.choice(list(intersect))
    
    least_idx = least_queries.index(sample_query)
    mid_idx = mid_queries.index(sample_query)
    most_idx = most_queries.index(sample_query)

    print("=" * 60)
    print("Showing examples of REJECTED samples for the following query:")
    print(f"Sample Query: \033[1;32m{sample_query}\033[0m") # 샘플 쿼리는 초록색으로 강조
    print("=" * 60)

    # 각 케이스별로 제거된 샘플을 보여줍니다.
    show_removed_samples(
        "Least Strict Case",
        sample_query,
        least[least_idx]['Removed_samples']
    )
    show_removed_samples(
        "Mid Strict Case",
        sample_query,
        mid[mid_idx]['Removed_samples']
    )
    show_removed_samples(
        "Most Strict Case",
        sample_query,
        most[most_idx]['Removed_samples']
    )

find_example(most, mid, least)

find_example(most, mid, least)

find_example(most, mid, least)

find_example(most, mid, least)

# 헬퍼 함수 1: 두 텍스트 간 겹치는 단어 강조
def highlight_intersection(text1, text2):
    """
    text1과 text2의 단어(토큰)를 비교하여 겹치는 단어를 강조 표시합니다.
    """
    words1 = set(text1.split())
    words2 = set(text2.split())
    common_words = words1.intersection(words2)

    highlighted_text2 = []
    for word in text2.split():
        if word in common_words:
            # ANSI 이스케이프 코드로 빨간색, 굵게 강조
            highlighted_text2.append(f"\033[1;31m{word}\033[0m")
        else:
            highlighted_text2.append(word)
    return " ".join(highlighted_text2)

# 헬퍼 함수 2: 제거된 샘플 중 최대 5개를 임의로 보여주는 함수
def show_removed_samples(case_name, sample_query, removed_list):
    """
    제거된 샘플 리스트에서 최대 5개를 임의로 골라 강조 표시와 함께 출력합니다.
    """
    print(f"--- {case_name} ({len(removed_list)} Removed) ---")
    if not removed_list:
        print("No sample left")
        return

    # 보여줄 샘플 수 결정 (최대 5개)
    num_to_show = min(len(removed_list), 5)
    
    # 임의 샘플 추출
    samples_to_show = random.sample(removed_list, num_to_show)

    for i, removed_sample in enumerate(samples_to_show):
        highlighted_text = highlight_intersection(sample_query, removed_sample)
        print(f"  Sample {i+1}:")
        # pprint 대신 print를 사용해야 ANSI 코드가 올바르게 렌더링됩니다.
        print(f"    {highlighted_text}")
    print("-" * 20)


# 메인 함수 수정
def find_example(most, mid, least):
    least_queries = [d['Chosen_sample'] for d in least]
    mid_queries = [d['Chosen_sample'] for d in mid]
    most_queries = [d['Chosen_sample'] for d in most]

    # 세 리스트에 모두 존재하는 쿼리들의 교집합을 찾습니다.
    intersect = set(least_queries) & set(mid_queries) & set(most_queries)
    
    if not intersect:
        print("모든 케이스에 공통적으로 존재하는 쿼리가 없습니다.")
        return

    # 교집합에서 임의의 샘플 쿼리를 하나 선택합니다.
    sample_query = random.choice(list(intersect))
    
    least_idx = least_queries.index(sample_query)
    mid_idx = mid_queries.index(sample_query)
    most_idx = most_queries.index(sample_query)

    print("=" * 60)
    print("Showing examples of REJECTED samples for the following query:")
    print(f"Sample Query: \033[1;32m{sample_query}\033[0m") # 샘플 쿼리는 초록색으로 강조
    print("=" * 60)

    # 각 케이스별로 제거된 샘플을 보여줍니다.
    show_removed_samples(
        "Least Strict Case",
        sample_query,
        least[least_idx]['Removed_samples']
    )
    show_removed_samples(
        "Mid Strict Case",
        sample_query,
        mid[mid_idx]['Removed_samples']
    )
    show_removed_samples(
        "Most Strict Case",
        sample_query,
        most[most_idx]['Removed_samples']
    )

import numpy as np

def data_stats(data_dir):
    for file in os.listdir(data_dir):
        if file.endswith(".jsonl"):
            data_name = file.split('_merged.jsonl')[0]
            data = open_jsonl(os.path.join(data_dir, file))
            print(f"{data_name} has {len(data)} records")
            avg_query_length = np.mean([len(d['query_and_response'][0]['content']) for d in data]  )
            print(f"Average Length of Queries : {avg_query_length}")

data_stats("final_merged")

def data_stats_deduped(data_dir):
    for file in os.listdir(data_dir):
        if file.endswith(".jsonl"):
            data_name = file.split('_merged.jsonl')[0]
            data = open_jsonl(os.path.join(data_dir, file))
            print(f"{data_name} has {len(data)} records")
            avg_query_length = np.mean([len(d['query_and_response'][0]['content']) for d in data if d['dedup_priority']==1]  )
            print(f"Average Length of Queries : {avg_query_length}")

data_stats_deduped("final_merged/dedup_dataset")

sample = open_jsonl("/mnt/raid6/johnny/projects/WBL/dedup/final_merged/engineering_merged.jsonl")
len(sample)

from collections import defaultdict

def group_by_key(data_list, key):
    """특정 키로 딕셔너리 리스트를 그룹화"""
    grouped = defaultdict(list)
    for item in data_list:
        grouped[item[key]].append(item)
    return dict(grouped)

dt = group_by_key(sample, '도메인_중분류')

dt.keys()

files = []
for f in os.listdir("new_data_0831"):
    if f.endswith(".jsonl"):
        files.append(open_jsonl(os.path.join("new_data_0831", f)))

old_files = {}
for f in os.listdir("final_merged"):
    if f.endswith(".jsonl") and not f.startswith("math"):
        category = f.split('_merged')[0]
        old_files[category] = open_jsonl(os.path.join("final_merged", f))

old_files.keys()

for cat, records in old_files.items():
    print(f"{cat} : {len(records)}")

math_count = 0
for file in files:
    for record in file:
        if record['도메인_대분류'] in old_files.keys():
            old_files[record['도메인_대분류']].append(record)
        else:
            math_count += 1
            # print(f"{record['도메인_대분류']} not found in the old files") 
print(math_count)

for cat, records in old_files.items():
    print(f"{cat} : {len(records)}")

data_dir = '250901/dedup_dataset'
post_dedup = defaultdict(list)
pre_dedup = defaultdict(list)
original_file = defaultdict(list)
for file in os.listdir(data_dir):
    if file.endswith("jsonl"):
        domain = file.split("_merged")[0]
        data = open_jsonl(os.path.join(data_dir, file))
        original_file[domain] = data
        pre_dedup[domain] = [len(get_query(record)) for record in data]
        post_dedup[domain] = [len(get_query(record)) for record in data if record['dedup_priority']==1 ]

# 전체 데이터의 길이 분포를 뽑아보자.
def show_length_dist(pre_dedup, post_dedup, save_dir = '250901/analysis'):
    for domain in pre_dedup.keys():

        lengths_A = pre_dedup[domain]
        lengths_B = post_dedup[domain]

        # --- 2. Create Logarithmically Spaced Bins ---
        # This is the key step for a good log-scaled histogram.
        # Instead of linear bins (e.g., [10, 20, 30]), we create bins that are
        # evenly spaced on a log scale (e.g., [1, 10, 100, 1000, ...]).
        min_val = 1  # Start bins at 1 since log(0) is undefined.
        max_val = max(max(lengths_A), max(lengths_B))
        # Create 50 bins from 1 up to the maximum value.
        # log_bins = np.logspace(np.log10(min_val), np.log10(max_val), num=50)

        # --- 3. Plot the Histograms ---
        plt.figure(figsize=(12, 7))

        # Plot histogram for List A
        plt.hist(lengths_A, bins=70, color='salmon', alpha=0.7, label='pre dedup', edgecolor='black')

        # Plot histogram for List B on the same axes
        plt.hist(lengths_B, bins=70, color='skyblue', alpha=0.7, label='post dedup', edgecolor='black')

        # --- 4. Set the X-axis to a Log Scale ---
        # plt.xscale('log')
        plt.yscale('log')

        # --- 5. Add Labels and Formatting ---
        plt.title(f'Log-Scaled Distribution of String Lengths for {domain}', fontsize=16)
        plt.xlabel('String Length (log scale)', fontsize=12)
        plt.ylabel('Frequency (Count)', fontsize=12)
        plt.legend()
        plt.grid(True, which="both", linestyle='--', linewidth=0.5) # 'which="both"' adds minor gridlines

        # --- 6. Show the Plot ---
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, f"{domain}_length_dist.png"))
        plt.show()


show_length_dist(pre_dedup, post_dedup)

for domain, length_list in post_dedup.items():
    long_queries = 0
    for i, length in enumerate(length_list):
        if length > 10000 and original_file[domain][i]['dedup_priority']==1:
            long_queries += 1

    print(f"{domain} has {long_queries} queries over length 10k")

long_samples = defaultdict(list)

for domain, length_list in pre_dedup.items():
    for i, length in enumerate(length_list):
        if length > 10000 and original_file[domain][i]['dedup_priority']==1:
            long_samples[domain].append((original_file[domain][i]['id'], get_query(original_file[domain][i])))
    long_samples[domain].sort(key=lambda item: len(item[1]), reverse=True)

def review_long_samples(long_samples_dict, bad_samples_dict, domain):
    """
    Iterates through samples, displays them for manual review,
    and records judgments, ensuring output appears before input.
    """
    bad_ids_for_domain = []

    for i, (samdataple_id, query) in enumerate(long_samples_dict[domain]):
        print("\n" + "="*50)
        print(f"Sample Index: {i} | Sample ID: {sample_id}")
        pprint(query[:5000])
        print('*****skipping*****')
        pprint(query[-5000:])
        print("="*50)
        
        # --- THE FIX IS HERE ---
        # Force the print buffer to flush to the screen
        sys.stdout.flush() 
        # -----------------------
        
        while True:
            judgment = input("Is this a bad sample? (y/n/quit): ").lower().strip()
            
            if judgment == 'y':
                bad_ids_for_domain.append(sample_id)
                print(f"  -> Marked as BAD. ID '{sample_id}' will be recorded.")
                break
            elif judgment == 'n':
                print("  -> Marked as GOOD. Skipping.")
                break
            elif judgment == 'quit':
                print("Quitting review session.")
                # Populate bad_samples with what we have so far before quitting
                if bad_ids_for_domain:
                    bad_samples_dict[domain] = bad_ids_for_domain
                return
            else:
                print("Invalid input. Please enter 'y', 'n', or 'quit'.")

    if bad_ids_for_domain:
        bad_samples_dict[domain] = bad_ids_for_domain


bad_samples = defaultdict(list)

for id in bad_samples['math']:
    print(lookup_data(original_file['math'], id))

review_long_samples(long_samples, bad_samples, 'math')



# Query length가 10000 이상인 샘플 찾기
def get_long_queries(post_dedup):
    

code_grouped_by_dataset = defaultdict(list)

for sample in original_file['code']:
    dataset= sample['id'].split('_')[0]
    code_grouped_by_dataset[dataset].append(sample)

code_grouped_by_dataset['aya']

# 내가 볼 데이터셋 : WildChat-1M, The-Tome, OpenMathInstruct-2, MetaMathQA
math = open_jsonl("/mnt/raid6/gjshim/WBL/data/250902/math_merged_975947_1742133_638544.jsonl")

df = pd.DataFrame(math)
cluster_counts = df['dedup_cluster'].value_counts()
cluster_counts

len(df)

dataset_names = ["aya", "lmsys-chat-1m", "NuminaMath-CoT", "Orca-Math"]
data_subset= defaultdict()

for d in dataset_names:
    data_subset[d] = df[df['id'].str.contains(d)]

cluster_counts = data_subset['aya']['dedup_cluster'].value_counts()
cluster_counts

def get_query(example):
    for conv_dict in example['query_and_response']:
        if conv_dict['from'] == 'user':
            text_content = str(conv_dict['content'])
            return text_content
        else: continue
    return example['query_and_response'][0]['content']

def get_cluster_examples(df, data):
    cluster_counts = df['dedup_cluster'].value_counts()
    cluster_counts_filtered = cluster_counts.drop("no_cluster", errors='ignore')
    
    cluster_name, cluster_count = cluster_counts_filtered.idxmax(), cluster_counts_filtered.max()
    print(f"최다 클러스터 : {cluster_name} with {cluster_count} samples")
    indices = list(df[df['dedup_cluster']==cluster_name].index)

    print(indices)

    return [get_query(l) for i, l in enumerate(data) if i in indices]

examples = get_cluster_examples(data_subset['Orca-Math'], math)

examples[:20]

temp_df=data_subset['Orca-Math']
sub_df = temp_df.sample(n=20, random_state=42)
examples = [get_query(l) for l in sub_df.to_dict('records')]
examples

dedup_files = defaultdict(list)
for domain, dataset in original_file.items():
    dedup_files[domain] = [ record for record in dataset if record['dedup_priority']==1]

def sample_queries(dedup_files, target_sample=500, save_dir='sampled_data'):
    for domain, dataset in dedup_files.items():
        print(f"Extracting {target_sample} iNuminaMath-CoTn **{domain}**...")
        df = pd.DataFrame({
            'id': record['id'],
            'source': record['id'].split('_')[0],
            'query': record['query_and_response'][0]['content'] if record['query_and_response'][0]['from']=='user' else record['query_and_response'][1]['content'],
        } for record in dataset)
        df['length'] = df['query'].str.len()
        source_counts = df['source'].value_counts()

        proportions = source_counts / len(df)
        proportional_samples = (proportions * target_sample).round().astype(int)

        diff = 300 - proportional_samples.sum()
        if diff != 0:
            proportional_samples[proportional_samples.idxmax()] += diff
        proportional_samples[proportional_samples < 1] = 1

        final_samples = []
        quantile_defs = {
            'rare': (0, 0.2),
            'medium-rare': (0.2, 0.4),
            'medium': (0.4, 0.6),
            'medium-welldone' : (0.6, 0.8),
            'welldone': (0.8, 1.0)
        }
        sample_dist = { k:0.2 for k in quantile_defs.keys()}

        for source_name, num_samples in proportional_samples.items():
            print(f"\tSampling from '{source_name}' (Target: {num_samples} samples)...")
            source_df = df[df['source'] == source_name].copy()
            
            # Stratify based on length quantiles
            for quantile_name, (q_low, q_high) in quantile_defs.items():
                # Determine the number of samples for this stratum
                n_stratum_samples = int(round(num_samples * sample_dist[quantile_name]))
                if n_stratum_samples == 0:
                    continue

                # Get the length boundaries for the current quantile
                low_len = source_df['length'].quantile(q_low)
                high_len = source_df['length'].quantile(q_high)
                
                # Filter the dataframe for this stratum
                stratum_df = source_df[(source_df['length'] >= low_len) & (source_df['length'] <= high_len)]
                
                # Take a random sample from the stratum
                # If not enough samples exist, take all of them
                actual_samples = min(n_stratum_samples, len(stratum_df))
                sampled_stratum = stratum_df.sample(n=actual_samples, random_state=42)
                
                final_samples.append(sampled_stratum)
                # print(f"  - From '{quantile_name}' group: Grabbed {len(sampled_stratum)} samples.")

        final_df = pd.concat(final_samples).reset_index(drop=True)
        sampled_data = []
        for id in final_df['id']:
            sampled_data.append(lookup_data(dataset, id))
        save_jsonl(f"sampled_data/{domain}_{target_sample}.jsonl", sampled_data)

sample_queries(dedup_files, 500)

