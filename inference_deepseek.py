from together import Together
import os
import json
from dotenv import load_dotenv

load_dotenv()
client = Together()

response = client.chat.completions.create(
  model="deepseek-ai/DeepSeek-V3.1",
  messages=[
#     {
#         "role": "system", 
#         "content": "Please reason step by step, and put your final answer within \boxed{}"
# #         "content": """
# #         Please reason step by step through your thinking process, then provide a clear explanation and put your final answer within \boxed{}.

# # You must:

# # 1. Show complete reasoning from start to finish
# # 2. Never stop without providing the final answer
# # 3. Use \boxed{} for numerical answers, expressions, or short results
# # 4. For proofs or explanations, provide structured reasoning followed by clear conclusions.
# #         """
#     },
    {
      "role": "user",
      "content": "Let  $f(x)$  and  $g(x)$  be strictly increasing linear functions from  $\\mathbb R $  to  $\\mathbb R $  such that  $f(x)$  is an integer if and only if  $g(x)$  is an integer. Prove that for any real number  $x$ ,  $f(x)-g(x)$  is an integer."
    }
  ],
    chat_template_kwargs={"thinking": True},
    max_tokens=16384,
    stream=False,  # Changed to False
)

message = response.choices[0].message

# Print reasoning if available
if hasattr(message, 'reasoning') and message.reasoning:
    print("<think>", message.reasoning, "</think>")

# Print content
if message.content:
    print(message.content)


    # stream = client.chat.completions.create(
#   model="deepseek-ai/DeepSeek-V3.1",
#   messages=[
#     {
#         "role": "system",
#         "content": "Please reason step by step, and put your final answer within \boxed{}"
#     },
#     {
#       "role": "user",
#       "content": "Prove that the set $D$ is uncountable by constructing a surjection from $D$ to the power set of $\\mathbb{N}$"
#     }
#   ],
#     chat_template_kwargs={"thinking": True},
#     max_tokens=16384,  # Set maximum output tokens
#     stream = True,
# )

# for chunk in stream:
#     # Check if chunk has choices before accessing
#     if chunk.choices and len(chunk.choices) > 0:
#         delta = chunk.choices[0].delta

#         # Show reasoning tokens if present
#         if hasattr(delta, "reasoning") and delta.reasoning:
#             print(delta.reasoning, end="", flush=True)

#         # Show content tokens if present
#         if hasattr(delta, "content") and delta.content:
#             print(delta.content, end="", flush=True)

# #print(response.choices[0].message.content)