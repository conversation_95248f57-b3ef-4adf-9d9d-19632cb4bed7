#!/usr/bin/env python3
"""
Script to randomly inject system prompts into query_and_response data.

Injection probabilities:
- "You are a helpful assistant." : 25%
- "You are a helpful assistant. Please think step by step." : 25%
- No injection : 50%

The system prompt dictionary will be placed first in the query_and_response list.
"""

import json
import random
import argparse
from typing import Dict, List, Any
from pathlib import Path


class SystemPromptInjector:
    """Class to handle system prompt injection into conversation data."""
    
    def __init__(self, seed: int = None):
        """
        Initialize the injector with optional random seed for reproducibility.
        
        Args:
            seed: Random seed for reproducible results
        """
        if seed is not None:
            random.seed(seed)
        
        # Define system prompts and their probabilities
        self.system_prompts = [
            {"from": "system", "content": "You are a helpful assistant."},
            {"from": "system", "content": "You are a helpful assistant. Please think step by step."},
            None  # No injection
        ]
        
        # Probabilities: 25%, 25%, 50%
        self.probabilities = [0.25, 0.25, 0.50]
    
    def inject_system_prompt(self, query_and_response: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Inject a system prompt into the query_and_response list.
        
        Args:
            query_and_response: List of conversation dictionaries
            
        Returns:
            Modified list with system prompt injected (if selected)
        """
        # Make a copy to avoid modifying the original
        modified_list = query_and_response.copy()
        
        # Randomly select a system prompt based on probabilities
        selected_prompt = random.choices(self.system_prompts, weights=self.probabilities)[0]
        
        if selected_prompt is not None:
            # Check if there's already a system message
            has_system = any(msg.get("from") == "system" for msg in modified_list)
            
            if has_system:
                # Replace existing system message
                for i, msg in enumerate(modified_list):
                    if msg.get("from") == "system":
                        modified_list[i] = selected_prompt
                        break
            else:
                # Insert at the beginning
                modified_list.insert(0, selected_prompt)
        
        return modified_list
    
    def process_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single data item and inject system prompt if applicable.
        
        Args:
            item: Dictionary containing the conversation data
            
        Returns:
            Modified item with system prompt injected
        """
        # Make a copy to avoid modifying the original
        processed_item = item.copy()
        
        if "query_and_response" in processed_item:
            processed_item["query_and_response"] = self.inject_system_prompt(
                processed_item["query_and_response"]
            )
        
        return processed_item
    
    def process_file(self, input_path: str, output_path: str) -> Dict[str, int]:
        """
        Process a JSONL file and inject system prompts.
        
        Args:
            input_path: Path to input JSONL file
            output_path: Path to output JSONL file
            
        Returns:
            Dictionary with statistics about the processing
        """
        input_file = Path(input_path)
        output_file = Path(output_path)
        
        if not input_file.exists():
            raise FileNotFoundError(f"Input file not found: {input_path}")
        
        # Create output directory if it doesn't exist
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        stats = {
            "total_items": 0,
            "injected_prompt_1": 0,  # "You are a helpful assistant."
            "injected_prompt_2": 0,  # "You are a helpful assistant. Please think step by step."
            "no_injection": 0
        }
        
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8') as outfile:
            
            for line_num, line in enumerate(infile, 1):
                try:
                    # Parse JSON line
                    item = json.loads(line.strip())
                    stats["total_items"] += 1
                    
                    # Store original query_and_response for comparison
                    original_qr = item.get("query_and_response", [])
                    
                    # Process the item
                    processed_item = self.process_item(item)
                    
                    # Update statistics
                    new_qr = processed_item.get("query_and_response", [])
                    if len(new_qr) > len(original_qr):
                        # System prompt was added
                        system_content = new_qr[0].get("content", "")
                        if system_content == "You are a helpful assistant.":
                            stats["injected_prompt_1"] += 1
                        elif system_content == "You are a helpful assistant. Please think step by step.":
                            stats["injected_prompt_2"] += 1
                    else:
                        # Check if system prompt was replaced
                        has_system_original = any(msg.get("from") == "system" for msg in original_qr)
                        has_system_new = any(msg.get("from") == "system" for msg in new_qr)
                        
                        if has_system_new and not has_system_original:
                            # System prompt was added (replaced existing)
                            for msg in new_qr:
                                if msg.get("from") == "system":
                                    system_content = msg.get("content", "")
                                    if system_content == "You are a helpful assistant.":
                                        stats["injected_prompt_1"] += 1
                                    elif system_content == "You are a helpful assistant. Please think step by step.":
                                        stats["injected_prompt_2"] += 1
                                    break
                        else:
                            stats["no_injection"] += 1
                    
                    # Write processed item to output file
                    json.dump(processed_item, outfile, ensure_ascii=False)
                    outfile.write('\n')
                    
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON on line {line_num}: {e}")
                    continue
                except Exception as e:
                    print(f"Error processing line {line_num}: {e}")
                    continue
        
        return stats


def main():
    """Main function to handle command line arguments and run the injection."""
    parser = argparse.ArgumentParser(
        description="Inject system prompts into query_and_response data"
    )
    parser.add_argument(
        "input_path",
        help="Path to input JSONL file"
    )
    parser.add_argument(
        "output_path", 
        help="Path to output JSONL file"
    )
    parser.add_argument(
        "--seed",
        type=int,
        help="Random seed for reproducible results"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Print detailed statistics"
    )
    
    args = parser.parse_args()
    
    # Initialize injector
    injector = SystemPromptInjector(seed=args.seed)
    
    try:
        # Process the file
        print(f"Processing {args.input_path}...")
        stats = injector.process_file(args.input_path, args.output_path)
        
        # Print results
        print(f"\nProcessing complete!")
        print(f"Output saved to: {args.output_path}")
        print(f"\nStatistics:")
        print(f"  Total items processed: {stats['total_items']}")
        print(f"  'You are a helpful assistant.': {stats['injected_prompt_1']} ({stats['injected_prompt_1']/stats['total_items']*100:.1f}%)")
        print(f"  'You are a helpful assistant. Please think step by step.': {stats['injected_prompt_2']} ({stats['injected_prompt_2']/stats['total_items']*100:.1f}%)")
        print(f"  No injection: {stats['no_injection']} ({stats['no_injection']/stats['total_items']*100:.1f}%)")
        
        if args.verbose:
            print(f"\nDetailed breakdown:")
            for key, value in stats.items():
                print(f"  {key}: {value}")
                
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
