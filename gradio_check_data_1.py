import gradio as gr
import json
import re

def load_jsonl_data(file_path):
    """JSONL 파일을 로드하고 데이터를 반환합니다."""
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    item = json.loads(line.strip())
                    data.append(item)
                except json.JSONDecodeError as e:
                    print(f"줄 {line_num}에서 JSON 파싱 오류: {e}")
                    continue
    except FileNotFoundError:
        print(f"파일을 찾을 수 없습니다: {file_path}")
    except Exception as e:
        print(f"파일 로드 중 오류 발생: {e}")
    
    return data

def extract_thinking_process(assistant_content):
    """assistant 응답에서 thinking 과정과 실제 출력을 분리합니다."""
    # <think>와 </think> 사이의 내용을 찾기
    thinking_pattern = r'<think>(.*?)</think>'
    thinking_match = re.search(thinking_pattern, assistant_content, re.DOTALL)
    
    if thinking_match:
        thinking_content = thinking_match.group(1).strip()
        # thinking 부분을 제거한 나머지가 실제 출력
        actual_output = re.sub(thinking_pattern, '', assistant_content, flags=re.DOTALL).strip()
    else:
        thinking_content = "thinking 과정이 없습니다."
        actual_output = assistant_content
    
    return thinking_content, actual_output

def get_conversation_data(data, index):
    """지정된 인덱스의 대화 데이터를 추출합니다."""
    if not data or index < 0 or index >= len(data):
        # 반환 값의 개수를 9개로 맞춰줍니다. (ID 추가)
        return "데이터 없음", "데이터 없음", "데이터 없음", "데이터 없음", "데이터 없음", 0, 0, 0, 0
    
    conversation = data[index]
    
    # ID 추출
    conversation_id = conversation.get('id', 'ID 없음')
    
    # 기본값 설정
    system_prompt = "시스템 프롬프트가 없습니다."
    user_input = "사용자 입력이 없습니다."
    thinking_process = "thinking 과정이 없습니다."
    assistant_output = "assistant 출력이 없습니다."
    
    # query_and_response에서 데이터 추출
    if 'query_and_response' in conversation:
        for message in conversation['query_and_response']:
            if message.get('from') == 'system':
                system_prompt = message.get('content', system_prompt)
            elif message.get('from') == 'user':
                # 여기서 user_input을 재구성합니다.
                user_content = message.get('content', '내용 없음')
                user_input = user_content # id는 라벨에서 처리
            elif message.get('from') == 'assistant':
                assistant_content = message.get('content', '')
                thinking_process, assistant_output = extract_thinking_process(assistant_content)
    
    # 문자 수 계산
    system_char_count = len(system_prompt)
    user_char_count = len(user_input)
    thinking_char_count = len(thinking_process)
    assistant_char_count = len(assistant_output)
    
    # conversation_id를 추가로 반환합니다.
    return system_prompt, user_input, thinking_process, assistant_output, conversation_id, system_char_count, user_char_count, thinking_char_count, assistant_char_count

def update_conversation(data, index):
    """대화 인덱스가 변경될 때 호출되는 함수입니다."""
    # 반환 변수 목록에 conversation_id 추가
    system_prompt, user_input, thinking_process, assistant_output, conversation_id, system_char_count, user_char_count, thinking_char_count, assistant_char_count = get_conversation_data(data, index)
    
    total_count = len(data) if data else 0
    index_info = f"대화 {index + 1} / {total_count}"
    
    # 라벨 정보 업데이트
    system_char_info = f"## 🔧 시스템 프롬프트 ({system_char_count:,}자)"
    # 사용자 입력 라벨에 ID를 포함시킵니다.
    user_char_info = f"## 👤 사용자 입력 (ID: {conversation_id}, {user_char_count:,}자)"
    thinking_char_info = f"## 🤔 Thinking 과정 ({thinking_char_count:,}자)"
    assistant_char_info = f"## 🤖 Assistant 최종 출력 ({assistant_char_count:,}자)"
    
    return system_prompt, user_input, thinking_process, assistant_output, index_info, system_char_info, user_char_info, thinking_char_info, assistant_char_info


def create_gradio_interface(jsonl_path):
    """Gradio 인터페이스를 생성합니다."""
    
    # 데이터 로드
    print("데이터를 로드하는 중...")
    data = load_jsonl_data(jsonl_path)
    total_conversations = len(data)
    print(f"총 {total_conversations}개의 대화를 로드했습니다.")
    
    # 커스텀 CSS 스타일
    custom_css = """
    /* 시스템 프롬프트 스타일 */
    #system_prompt textarea {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
        font-size: 10px !important;
        line-height: 1.5 !important;
    }
    
    /* 사용자 입력 스타일 */
    #user_input textarea {
        font-family: 'Malgun Gothic', 'Apple SD Gothic Neo', sans-serif !important;
        font-size: 10px !important;
        line-height: 1.6 !important;
    }
    
    /* Thinking 과정 스타일 */
    #thinking_process textarea {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
        font-size: 10px !important;
        line-height: 1.4 !important;
        color: #555 !important;
    }
    
    /* Assistant 출력 스타일 */
    #assistant_output textarea {
        font-family: 'Malgun Gothic', 'Apple SD Gothic Neo', sans-serif !important;
        font-size: 10px !important;
        line-height: 1.6 !important;
        color: #2c3e50 !important;
    }
    
    /* 글꼴 크기 조정 슬라이더 컨테이너 */
    .font-controls {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 8px;
        margin: 10px 0;
    }
    """

    # 인라인 math 규칙
    latex_delimiters = [
        {"left": "$$", "right": "$$", "display": True}, # 블록 수식
        {"left": "$", "right": "$", "display": False} # 인라인 수식
    ]
    
    # Gradio 인터페이스 구성
    with gr.Blocks(title="데이터 검토 도구", theme=gr.themes.Soft(), css=custom_css) as demo:
        gr.Markdown("# 📊 JSONL 데이터 검토 도구")
        gr.Markdown(f"**파일**: `{jsonl_path}`  \n**총 대화 수**: {total_conversations}")
        
        # 글꼴 크기 조정 섹션
        with gr.Accordion("🎨 글꼴 설정", open=False):
            with gr.Row(elem_classes="font-controls"):
                system_font_size = gr.Slider(
                    minimum=10, maximum=24, step=1, value=14,
                    label="시스템 프롬프트 글꼴 크기", interactive=True
                )
                user_font_size = gr.Slider(
                    minimum=10, maximum=24, step=1, value=15,
                    label="사용자 입력 글꼴 크기", interactive=True
                )
            with gr.Row(elem_classes="font-controls"):
                thinking_font_size = gr.Slider(
                    minimum=10, maximum=24, step=1, value=13,
                    label="Thinking 과정 글꼴 크기", interactive=True
                )
                assistant_font_size = gr.Slider(
                    minimum=10, maximum=24, step=1, value=15,
                    label="Assistant 출력 글꼴 크기", interactive=True
                )
        
        # 인덱스 선택 슬라이더
        with gr.Row():
            index_slider = gr.Slider(
                minimum=0, 
                maximum=max(0, total_conversations - 1), 
                step=1, 
                value=0, 
                label="대화 선택",
                interactive=True
            )
            index_info = gr.Textbox(
                value=f"대화 1 / {total_conversations}",
                label="현재 위치",
                interactive=False,
                max_lines=1
            )
        
        # 네비게이션 버튼
        with gr.Row():
            prev_btn = gr.Button("⬅️ 이전", variant="secondary", size="sm")
            next_btn = gr.Button("➡️ 다음", variant="secondary", size="sm")
        
        # 시스템 프롬프트와 사용자 입력 섹션 - 문자 수 라벨 추가
        with gr.Row():
            system_char_label = gr.Markdown("## 🔧 시스템 프롬프트 (0자)")
            user_char_label = gr.Markdown("## 👤 사용자 입력 (0자)")
        with gr.Row():
            system_prompt_box = gr.Textbox(
                label="시스템 프롬프트",
                lines=5,
                max_lines=10,
                interactive=False,
                show_copy_button=True,
                elem_id="system_prompt"
            )
            user_input_box = gr.Markdown(
                elem_classes="markdown-box",
                latex_delimiters=latex_delimiters
            )
        
        # Thinking 과정 섹션 - 문자 수 라벨 추가
        with gr.Row():
            thinking_char_label = gr.Markdown("## 🤔 Thinking 과정 (0자)")
        with gr.Row():
            thinking_box = gr.Markdown(
                elem_classes="markdown-box",
                latex_delimiters =latex_delimiters 
            )
        
        # Assistant 출력 섹션 - 문자 수 라벨 추가
        with gr.Row():
            assistant_char_label = gr.Markdown("## 🤖 Assistant 최종 출력 (0자)")
        with gr.Row():
            assistant_output_box = gr.Markdown(
                elem_classes="markdown-box",
                latex_delimiters =latex_delimiters 
            )
        
        # 초기 데이터 로드
        if data:
            initial_system, initial_user, initial_thinking, initial_output, initial_conv, sys_count, user_count, think_count, assist_count = get_conversation_data(data, 0)
            system_prompt_box.value = initial_system
            user_input_box.value = initial_user
            thinking_box.value = initial_thinking
            assistant_output_box.value = initial_output
            # 초기 문자 수 라벨 업데이트
            system_char_label.value = f"## 🔧 시스템 프롬프트 ({sys_count:,}자)"
            user_char_label.value = f"## 👤 사용자 입력 (ID: {initial_conv}, {user_count:,}자)"
            thinking_char_label.value = f"## 🤔 Thinking 과정 ({think_count:,}자)"
            assistant_char_label.value = f"## 🤖 Assistant 최종 출력 ({assist_count:,}자)"
        
        # 이벤트 핸들러 설정
        def on_index_change(index):
            return update_conversation(data, int(index))
        
        index_slider.change(
            fn=on_index_change,
            inputs=[index_slider],
            outputs=[system_prompt_box, user_input_box, thinking_box, assistant_output_box, index_info, system_char_label, user_char_label, thinking_char_label, assistant_char_label]
        )
        
        def go_previous(current_index):
            new_index = max(0, current_index - 1)
            results = update_conversation(data, new_index)
            return [new_index] + list(results)
            
        def go_next(current_index):
            new_index = min(total_conversations - 1, current_index + 1)
            results = update_conversation(data, new_index)
            return [new_index] + list(results)
        
        prev_btn.click(
            fn=go_previous,
            inputs=[index_slider],
            outputs=[index_slider, system_prompt_box, user_input_box, thinking_box, assistant_output_box, index_info, system_char_label, user_char_label, thinking_char_label, assistant_char_label]
        )
        
        next_btn.click(
            fn=go_next,
            inputs=[index_slider],
            outputs=[index_slider, system_prompt_box, user_input_box, thinking_box, assistant_output_box, index_info, system_char_label, user_char_label, thinking_char_label, assistant_char_label]
        )
        
        # 글꼴 크기 변경 JavaScript 함수
        font_change_js = """
        function(system_size, user_size, thinking_size, assistant_size) {
            setTimeout(function() {
                const systemTextarea = document.querySelector('#system_prompt textarea');
                const userTextarea = document.querySelector('#user_input textarea');
                const thinkingTextarea = document.querySelector('#thinking_process textarea');
                const assistantTextarea = document.querySelector('#assistant_output textarea');
                
                if (systemTextarea) systemTextarea.style.fontSize = system_size + 'px';
                if (userTextarea) userTextarea.style.fontSize = user_size + 'px';
                if (thinkingTextarea) thinkingTextarea.style.fontSize = thinking_size + 'px';
                if (assistantTextarea) assistantTextarea.style.fontSize = assistant_size + 'px';
            }, 100);
            return [system_size, user_size, thinking_size, assistant_size];
        }
        """
        
        # 글꼴 크기 변경 이벤트 핸들러 (각 슬라이더별로 개별 설정)
        system_font_size.change(
            fn=None,
            inputs=[system_font_size, user_font_size, thinking_font_size, assistant_font_size],
            outputs=[system_font_size, user_font_size, thinking_font_size, assistant_font_size],
            js=font_change_js
        )
        
        user_font_size.change(
            fn=None,
            inputs=[system_font_size, user_font_size, thinking_font_size, assistant_font_size],
            outputs=[system_font_size, user_font_size, thinking_font_size, assistant_font_size],
            js=font_change_js
        )
        
        thinking_font_size.change(
            fn=None,
            inputs=[system_font_size, user_font_size, thinking_font_size, assistant_font_size],
            outputs=[system_font_size, user_font_size, thinking_font_size, assistant_font_size],
            js=font_change_js
        )
        
        assistant_font_size.change(
            fn=None,
            inputs=[system_font_size, user_font_size, thinking_font_size, assistant_font_size],
            outputs=[system_font_size, user_font_size, thinking_font_size, assistant_font_size],
            js=font_change_js
        )
        
        # 사용법 안내
        with gr.Accordion("📖 사용법", open=False):
            gr.Markdown("""
            ### 사용 방법
            1. **슬라이더 조작**: 상단의 슬라이더를 움직여 원하는 대화를 선택하세요.
            2. **버튼 네비게이션**: '이전'/'다음' 버튼으로 순차적으로 대화를 탐색하세요.
            3. **복사 기능**: 각 텍스트 박스 우측의 복사 버튼을 클릭하여 내용을 클립보드에 복사할 수 있습니다.
            4. **스크롤**: Thinking 과정과 Assistant 출력이 길 수 있으니 스크롤하여 전체 내용을 확인하세요.
            5. **글꼴 설정**: '🎨 글꼴 설정' 아코디언을 열어 각 섹션별로 글꼴 크기를 조정할 수 있습니다.
            
            ### 데이터 구조
            - **시스템 프롬프트**: AI의 행동을 지시하는 초기 설정 (고정폭 폰트)
            - **사용자 입력**: 사용자가 제출한 질문이나 요청 (한글 최적화 폰트)
            - **Thinking 과정**: AI의 내부 사고 과정 (`<think>` 태그 내부, 고정폭 폰트)
            - **Assistant 출력**: 사용자에게 제공되는 최종 답변 (한글 최적화 폰트)
            
            ### 글꼴 특징
            - **시스템 프롬프트 & Thinking 과정**: 고정폭 폰트 (Consolas, Monaco, Courier New)로 코드나 구조화된 텍스트에 적합
            - **사용자 입력 & Assistant 출력**: 한글 최적화 폰트 (맑은 고딕, Apple SD Gothic Neo)로 가독성 향상
            - **글꼴 크기**: 10px~24px 범위에서 1px 단위로 조정 가능
            """)
    
    return demo

if __name__ == "__main__":
    jsonl_path = "math_500_response_generated.jsonl" # 이거 변경하면 됨

    demo = create_gradio_interface(jsonl_path)
    demo.launch(
        share=False,            # 공개 링크 생성 안함
    )
