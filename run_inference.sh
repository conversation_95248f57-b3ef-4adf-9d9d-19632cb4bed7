#!/bin/bash

# Inference Script Runner
# Based on configurations from generate_response_api.py

# Default configuration
MODEL="deepseek-ai/DeepSeek-V3.1"
API_KEY_FILE="secret/deepseek.key"  # Default from generate_response_api.py
MAX_CONCURRENT=40
TIMEOUT_S=60.0
MAX_RETRIES=2
ENABLE_THINKING="--enable_thinking"

# Help function
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -m, --model MODEL           Model name (default: $MODEL)"
    echo "  -k, --api-key KEY           API key (overrides key file)"
    echo "  -f, --api-key-file FILE     API key file path (default: $API_KEY_FILE)"
    echo "  -i, --input PATH            Input JSONL file for batch processing"
    echo "  -o, --output PATH           Output JSONL file for batch processing"
    echo "  -p, --prompt TEXT           Single prompt for inference"
    echo "  -s, --system-prompt TEXT    System prompt"
    echo "  -c, --max-concurrent N      Max concurrent requests (default: $MAX_CONCURRENT)"
    echo "  -t, --timeout N             Timeout in seconds (default: $TIMEOUT_S)"
    echo "  -r, --max-retries N         Max retries (default: $MAX_RETRIES)"
    echo "  --no-thinking               Disable thinking mode"
    echo "  -h, --help                  Show this help"
    echo ""
    echo "Examples:"
    echo "  # Single prompt inference"
    echo "  $0 -p \"What is 2+2?\""
    echo ""
    echo "  # Batch processing"
    echo "  $0 -i input.jsonl -o output.jsonl"
    echo ""
    echo "  # With custom model and system prompt"
    echo "  $0 -m \"deepseek-ai/DeepSeek-V3.1\" -s \"You are a math tutor\" -p \"Solve x^2 = 4\""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--model)
            MODEL="$2"
            shift 2
            ;;
        -k|--api-key)
            API_KEY="$2"
            shift 2
            ;;
        -f|--api-key-file)
            API_KEY_FILE="$2"
            shift 2
            ;;
        -i|--input)
            INPUT_PATH="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_PATH="$2"
            shift 2
            ;;
        -p|--prompt)
            PROMPT="$2"
            shift 2
            ;;
        -s|--system-prompt)
            SYSTEM_PROMPT="$2"
            shift 2
            ;;
        -c|--max-concurrent)
            MAX_CONCURRENT="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT_S="$2"
            shift 2
            ;;
        -r|--max-retries)
            MAX_RETRIES="$2"
            shift 2
            ;;
        --no-thinking)
            ENABLE_THINKING=""
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate inputs
if [[ -z "$PROMPT" && (-z "$INPUT_PATH" || -z "$OUTPUT_PATH") ]]; then
    echo "Error: Provide either --prompt for single inference or both --input and --output for batch processing"
    show_help
    exit 1
fi

# Build command
CMD="python inference.py"
CMD="$CMD --model \"$MODEL\""
CMD="$CMD --max_concurrent $MAX_CONCURRENT"
CMD="$CMD --timeout_s $TIMEOUT_S"
CMD="$CMD --max_retries $MAX_RETRIES"

# Add API key
if [[ -n "$API_KEY" ]]; then
    CMD="$CMD --api_key \"$API_KEY\""
elif [[ -f "$API_KEY_FILE" ]]; then
    CMD="$CMD --api_key_file \"$API_KEY_FILE\""
else
    echo "Warning: No API key provided. Will try TOGETHER_API_KEY environment variable."
fi

# Add thinking mode
if [[ -n "$ENABLE_THINKING" ]]; then
    CMD="$CMD $ENABLE_THINKING"
fi

# Add system prompt if provided
if [[ -n "$SYSTEM_PROMPT" ]]; then
    CMD="$CMD --system_prompt \"$SYSTEM_PROMPT\""
fi

# Add single prompt or batch processing
if [[ -n "$PROMPT" ]]; then
    CMD="$CMD --prompt \"$PROMPT\""
    echo "=== Single Prompt Inference ==="
    echo "Model: $MODEL"
    echo "Prompt: $PROMPT"
    if [[ -n "$SYSTEM_PROMPT" ]]; then
        echo "System Prompt: $SYSTEM_PROMPT"
    fi
    echo ""
else
    CMD="$CMD --input_path \"$INPUT_PATH\" --output_path \"$OUTPUT_PATH\""
    echo "=== Batch Processing ==="
    echo "Model: $MODEL"
    echo "Input: $INPUT_PATH"
    echo "Output: $OUTPUT_PATH"
    echo "Max Concurrent: $MAX_CONCURRENT"
    if [[ -n "$SYSTEM_PROMPT" ]]; then
        echo "System Prompt: $SYSTEM_PROMPT"
    fi
    echo ""
    
    # Check if input file exists
    if [[ ! -f "$INPUT_PATH" ]]; then
        echo "Error: Input file '$INPUT_PATH' not found"
        exit 1
    fi
    
    # Create output directory if needed
    OUTPUT_DIR=$(dirname "$OUTPUT_PATH")
    if [[ "$OUTPUT_DIR" != "." && ! -d "$OUTPUT_DIR" ]]; then
        echo "Creating output directory: $OUTPUT_DIR"
        mkdir -p "$OUTPUT_DIR"
    fi
fi

# Execute command
echo "Running: $CMD"
echo ""
eval $CMD

echo ""
echo "Inference completed!"
