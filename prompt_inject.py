import jsonlines
import random, os
from datasketch import MinHash
from datasketch import MinHashLSH
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from collections import defaultdict, Counter
import numpy as np
from pprint import pprint
import sys

def open_jsonl(file_path):
    with jsonlines.open(file_path) as f:
        return [l for l in f]

math = open_jsonl("/mnt/raid6/gjshim/WBL/data/250902/math_merged_975947_1742133_638544.jsonl")
df = pd.DataFrame(math)

