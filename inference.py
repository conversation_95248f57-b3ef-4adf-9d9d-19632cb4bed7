#!/usr/bin/env python3
"""
Inference script based on generate_response_api.py configurations
Supports both single queries and batch processing with Together API
"""

import os
import sys
import asyncio
import logging
import jsonlines
import argparse
import json
from typing import List, Tuple, Any, Dict, Optional
from pathlib import Path

from together import Together, AsyncTogether
from tqdm import tqdm


class InferenceClient:
    """
    Inference client based on AsyncGenerator from generate_response_api.py
    Supports both single inference and batch processing
    """
    
    def __init__(self, model_name: str, api_key: str, timeout_s: float = 60.0, max_retries: int = 2):
        """
        Initialize the inference client
        
        Args:
            model_name: Model name to use for inference
            api_key: Together API key
            timeout_s: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
        """
        self.model_name = model_name
        self.timeout_s = timeout_s
        self.max_retries = max_retries
        
        # Set API key
        os.environ["TOGETHER_API_KEY"] = api_key
        
        # Initialize Together client
        self.client = AsyncTogether()
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        logging.basicConfig(level=logging.INFO, stream=sys.stdout)
        self.logger.setLevel(logging.INFO)
        
        self.logger.info(f"Initialized InferenceClient with model: {model_name}")

    def log(self, message: str):
        """Log a message"""
        self.logger.info(message)

    async def generate_single(self, prompt: str, system_prompt: Optional[str] = None, 
                            tools: Optional[List[Dict]] = None, enable_thinking: bool = True) -> Tuple[str, Optional[Dict]]:
        """
        Generate response for a single prompt
        
        Args:
            prompt: User prompt
            system_prompt: Optional system prompt
            tools: Optional list of tools for function calling
            enable_thinking: Whether to enable thinking mode
            
        Returns:
            Tuple of (response_text, tool_info)
        """
        messages = []
        
        # Add system message if provided
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        # Add user message
        messages.append({"role": "user", "content": prompt})
        
        kwargs = {
            "model": self.model_name,
            "messages": messages,
            "stream": False,
        }
        
        # Add thinking mode if enabled
        if enable_thinking:
            kwargs["chat_template_kwargs"] = {"thinking": True}
        
        # Add tools and tool_choice if tools are present
        if tools:
            kwargs["tools"] = tools
            kwargs["tool_choice"] = "auto"
        
        try:
            out = await self.client.chat.completions.create(**kwargs)
            
            message = out.choices[0].message
            result = ""
            tool_info = None
            
            # Add reasoning if available
            if hasattr(message, 'reasoning') and message.reasoning:
                result += "<think> " + message.reasoning + " </think> "
            
            # Add content if available
            if message.content:
                result += message.content
            
            # Extract tool information separately
            if hasattr(message, 'tool_calls') and message.tool_calls:
                tool_info = {
                    "tool_calls": [],
                    "finish_reason": out.choices[0].finish_reason
                }
                for tool_call in message.tool_calls:
                    tool_info["tool_calls"].append({
                        "id": getattr(tool_call, 'id', None),
                        "type": getattr(tool_call, 'type', 'function'),
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments
                        }
                    })
            
            # Handle legacy function call format
            elif hasattr(message, 'function_call') and message.function_call:
                tool_info = {
                    "function_call": {
                        "name": message.function_call.name,
                        "arguments": message.function_call.arguments
                    },
                    "finish_reason": out.choices[0].finish_reason
                }
            
            # Add finish reason if it's not normal completion
            if not tool_info and out.choices[0].finish_reason != "stop":
                tool_info = {"finish_reason": out.choices[0].finish_reason}
            
            return result if result else "[No content generated]", tool_info
            
        except Exception as e:
            self.logger.error(f"Error in generate_single: {e}")
            return f"[ERROR] {type(e).__name__}: {e}", None

    async def generate_batch(self, prompts: List[Tuple[str, str, Optional[str], Optional[List[Dict]]]], 
                           max_concurrent: int = 40, desc: str = "batch_inference") -> List[Tuple[str, str, Optional[Dict]]]:
        """
        Generate responses for a batch of prompts
        
        Args:
            prompts: List of tuples (request_id, prompt, system_prompt, tools)
            max_concurrent: Maximum concurrent requests
            desc: Description for progress bar
            
        Returns:
            List of tuples (request_id, response, tool_info)
        """
        sem = asyncio.Semaphore(max_concurrent)
        results: List[Optional[Tuple[str, str, Optional[Dict]]]] = [None] * len(prompts)

        async def _worker(i, request_id, prompt, system_prompt, tools):
            async with sem:
                try:
                    result, tool_info = await self.generate_single(prompt, system_prompt, tools)
                    results[i] = (request_id, result, tool_info)
                except Exception as e:
                    results[i] = (request_id, f"[ERROR] {type(e).__name__}: {e}", None)

        tasks = [asyncio.create_task(_worker(i, rid, pmt, sys_pmt, tools)) 
                for i, (rid, pmt, sys_pmt, tools) in enumerate(prompts)]

        # Show progress with tqdm
        for fut in tqdm(asyncio.as_completed(tasks), total=len(tasks), desc=desc):
            await fut

        return results  # type: ignore

    def load_jsonl(self, file_path: str) -> List[Dict[str, Any]]:
        """Load data from JSONL file"""
        data = []
        try:
            with jsonlines.open(file_path) as reader:
                for line in reader:
                    data.append(line)
        except Exception as e:
            self.logger.error(f"Error loading {file_path}: {e}")
            raise
        return data

    def save_jsonl(self, data: List[Dict[str, Any]], file_path: str):
        """Save data to JSONL file"""
        os.makedirs(os.path.dirname(file_path) or ".", exist_ok=True)
        try:
            with jsonlines.open(file_path, mode='w') as writer:
                writer.write_all(data)
        except Exception as e:
            self.logger.error(f"Error saving to {file_path}: {e}")
            raise

    async def process_dataset(self, input_path: str, output_path: str, 
                            max_concurrent: int = 40, system_prompt: Optional[str] = None):
        """
        Process a dataset from JSONL file
        
        Args:
            input_path: Path to input JSONL file
            output_path: Path to output JSONL file
            max_concurrent: Maximum concurrent requests
            system_prompt: Optional system prompt to use for all queries
        """
        self.log(f"Processing dataset: {input_path} -> {output_path}")
        
        # Load input data
        data = self.load_jsonl(input_path)
        self.log(f"Loaded {len(data)} samples")
        
        # Prepare prompts
        prompts = []
        for i, item in enumerate(data):
            request_id = item.get("id", str(i))
            
            # Extract prompt from different possible formats
            prompt = ""
            if "query" in item:
                prompt = item["query"]
            elif "prompt" in item:
                prompt = item["prompt"]
            elif "question" in item:
                prompt = item["question"]
            elif "query_and_response" in item:
                # Extract from conversation format
                for msg in item["query_and_response"]:
                    if msg.get("from") == "user" or msg.get("role") == "user":
                        prompt = msg.get("content", "")
                        break
            
            if not prompt:
                self.log(f"Warning: No prompt found for item {request_id}")
                continue
            
            # Extract tools if present (similar to generate_response_api.py)
            tools = None
            if "query_and_response" in item:
                for message in item["query_and_response"]:
                    if message.get("from") == "system" and "tool_calls" in message:
                        tools = self._extract_tools(message["tool_calls"])
                        break
            
            prompts.append((request_id, prompt, system_prompt, tools))
        
        self.log(f"Prepared {len(prompts)} prompts for processing")
        
        # Process in batches
        results = await self.generate_batch(prompts, max_concurrent)
        
        # Update original data with results
        result_dict = {rid: (response, tool_info) for rid, response, tool_info in results}
        
        for item in data:
            request_id = item.get("id", str(data.index(item)))
            if request_id in result_dict:
                response, tool_info = result_dict[request_id]
                
                # Add response to the item
                item["generated_response"] = response
                if tool_info:
                    item["tool_info"] = tool_info
                
                # Also update query_and_response format if it exists
                if "query_and_response" in item:
                    assistant_msg = {"role": "assistant", "content": response}
                    if tool_info:
                        if "tool_calls" in tool_info:
                            assistant_msg["tool_calls"] = tool_info["tool_calls"]
                        if "function_call" in tool_info:
                            assistant_msg["function_call"] = tool_info["function_call"]
                        if "finish_reason" in tool_info:
                            assistant_msg["finish_reason"] = tool_info["finish_reason"]
                    
                    item["query_and_response"].append(assistant_msg)
        
        # Save results
        self.save_jsonl(data, output_path)
        self.log(f"Saved results to {output_path}")

    def _extract_tools(self, tool_calls: List[Dict]) -> List[Dict]:
        """Extract tools from tool_calls (similar to generate_response_api.py)"""
        tools = []
        for tool_call in tool_calls:
            if tool_call.get("type") == "function" and "function" in tool_call:
                function_info = tool_call["function"]

                # Skip malformed entries
                if "parameter_name" in function_info or ("name" not in function_info and "type" in function_info):
                    continue

                if "name" not in function_info:
                    continue

                tool_def = {
                    "type": "function",
                    "function": {
                        "name": function_info["name"],
                        "description": function_info.get("description", ""),
                        "parameters": {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    }
                }

                # Handle parameters
                if "parameters" in function_info:
                    for param_name, param_info in function_info["parameters"].items():
                        if isinstance(param_info, dict):
                            tool_def["function"]["parameters"]["properties"][param_name] = {
                                "type": param_info.get("type", "string"),
                                "description": param_info.get("description", "")
                            }
                            if param_info.get("default") is not None:
                                tool_def["function"]["parameters"]["properties"][param_name]["default"] = param_info["default"]
                        else:
                            tool_def["function"]["parameters"]["properties"][param_name] = {
                                "type": str(param_info) if param_info else "string",
                                "description": ""
                            }

                tools.append(tool_def)

        return tools


async def main():
    parser = argparse.ArgumentParser(description="Inference script based on generate_response_api.py")
    parser.add_argument("--model", type=str, default="deepseek-ai/DeepSeek-V3.1",
                       help="Model name to use")
    parser.add_argument("--api_key", type=str, help="Together API key (or set TOGETHER_API_KEY env var)")
    parser.add_argument("--api_key_file", type=str, help="Path to file containing API key")
    parser.add_argument("--input_path", type=str, help="Input JSONL file path")
    parser.add_argument("--output_path", type=str, help="Output JSONL file path")
    parser.add_argument("--prompt", type=str, help="Single prompt for inference")
    parser.add_argument("--system_prompt", type=str, help="System prompt")
    parser.add_argument("--max_concurrent", type=int, default=40, help="Maximum concurrent requests")
    parser.add_argument("--timeout_s", type=float, default=60.0, help="Request timeout in seconds")
    parser.add_argument("--max_retries", type=int, default=2, help="Maximum retries")
    parser.add_argument("--enable_thinking", action="store_true", default=True, help="Enable thinking mode")

    args = parser.parse_args()

    # Get API key
    api_key = args.api_key
    if not api_key and args.api_key_file:
        try:
            with open(args.api_key_file, 'r') as f:
                api_key = f.read().strip()
        except Exception as e:
            print(f"Error reading API key file: {e}")
            sys.exit(1)

    if not api_key:
        api_key = os.getenv("TOGETHER_API_KEY")

    if not api_key:
        print("Error: API key not provided. Use --api_key, --api_key_file, or set TOGETHER_API_KEY env var")
        sys.exit(1)

    # Initialize client
    client = InferenceClient(
        model_name=args.model,
        api_key=api_key,
        timeout_s=args.timeout_s,
        max_retries=args.max_retries
    )

    # Single prompt inference
    if args.prompt:
        print("=== Single Prompt Inference ===")
        response, tool_info = await client.generate_single(
            prompt=args.prompt,
            system_prompt=args.system_prompt,
            enable_thinking=args.enable_thinking
        )

        print(f"Response: {response}")
        if tool_info:
            print(f"Tool Info: {json.dumps(tool_info, indent=2)}")

    # Dataset processing
    elif args.input_path and args.output_path:
        print("=== Dataset Processing ===")
        await client.process_dataset(
            input_path=args.input_path,
            output_path=args.output_path,
            max_concurrent=args.max_concurrent,
            system_prompt=args.system_prompt
        )

    else:
        print("Error: Provide either --prompt for single inference or --input_path and --output_path for dataset processing")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
